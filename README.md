# Getting Started with nv2-core-header-ui 

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app) and was configured with [CRACO](https://github.com/dilanx/craco).\
Before installing dependencies, make sure you have Node.js v16.

Project uses two private gitlab packages:
- [nv2-pkg-js-shared-components](https://gitlabnv2.flyaps.com/nv2/pkg/js/nv2-pkg-js-shared-components)
- [nv2-pkg-js-theme](https://gitlabnv2.flyaps.com/nv2/pkg/js/nv2-pkg-js-theme)


For authorization in development mode need to run the application [nv2-core-auth-keycloak-ui](https://gitlabnv2.flyaps.com/nv2/core/nv2-core-auth-keycloak-ui).\
See more [details](https://nextgenclearing.atlassian.net/wiki/spaces/V2D/pages/211714049/OAuth2+Proxy#Working-with-protected-API-locally).

### Runtime|env variables

1. To work with nv2-core-auth-keycloak-ui need .env with url variables. See [example.env](example.env)
2. To get API URLs and other environment-specific configurations, the application makes a request to `/settings/config.json`.\
   For example:\
   `{`\
    `apiUrl: "https://example-api"`\
    `keycloakLogoutUrl: ''` _read more about this variable in [Single Loogout](https://nextgenclearing.atlassian.net/wiki/spaces/V2D/pages/211714049/OAuth2+Proxy#Single-Logout-(SLO)) and [Sign Out](https://nextgenclearing.atlassian.net/wiki/spaces/V2D/pages/211714049/OAuth2+Proxy#Sign-out)_\
    `}`

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3001](http://localhost:3001) to view it in your browser.

The page will reload when you make changes.\
You may also see any lint errors in the console.

### `npm run lint`

[ESLint](https://github.com/eslint/eslint) statically analyzes .js and .jsx code to quickly find problems.\
Uses [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)

### `npm run stylelint`

[Stylelint](https://github.com/stylelint/stylelint) that helps avoid errors and enforce conventions in styles\
Uses [standard shareable SCSS config for Stylelint](https://www.npmjs.com/package/stylelint-config-standard-scss)

### `npm test`

Launches the [Jest](https://jestjs.io/) in the interactive watch mode.

### `npm test:coverage`

Gets a code coverage report.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can't go back!**

If you aren't satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you're on your own.

You don't have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn't feel obligated to use this feature. However we understand that this tool wouldn't be useful if you couldn't customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

### `npm run build` fails to minify

This section has moved here: [https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)
