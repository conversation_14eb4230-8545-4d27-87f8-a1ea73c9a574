const sassResourcesLoader = require('craco-sass-resources-loader');
// eslint-disable-next-line import/no-extraneous-dependencies
const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin');
const { whenProd, whenDev } = require('@craco/craco');
const { dependencies } = require('./package.json');

module.exports = {
  plugins: [
    {
      plugin: sassResourcesLoader,
      options: {
        resources: ['node_modules/@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss', 'src/assets/styles/variables.scss'],
      },
    },
  ],
  webpack: {
    configure: (webpackConfig) => {
       
      webpackConfig.plugins = [
        ...webpackConfig.plugins,
        new ModuleFederationPlugin({
          name: 'header',
          filename: 'remoteEntry.js',
          exposes: {
            './Header': './src/AppProvider.jsx',
          },
          shared: {
            ...dependencies,
            react: { eager: true, requiredVersion: dependencies.react },
            'react-dom': {
              eager: true,
              requiredVersion: dependencies['react-dom'],
            },
            '@mui/material': {
              eager: true,
              requiredVersion: dependencies['@mui/material'],
            },
            '@mui/styles': { eager: true, requiredVersion: dependencies['@mui/styles'] },
            '@nv2/nv2-pkg-js-shared-components': { eager: true },
            '@nv2/nv2-pkg-js-theme': { eager: true },
            axios: { eager: true, requiredVersion: dependencies.axios },
            history: { eager: true },
            'prop-types': { eager: true, requiredVersion: dependencies['prop-types'] },
            'react-cookie': { eager: true },
            'react-redux': { eager: true },
            'react-router-dom': { eager: true, requiredVersion: dependencies['react-router-dom'] },
            'redux-logger': { eager: true },
            '@reduxjs/toolkit': { eager: true },
          },
        }),
      ];

       
      webpackConfig.output = {
        ...webpackConfig.output,
        // for start as container part
        // ...whenDev(() => ({ publicPath: 'auto', clean: true })),
        // for start as independent application
        ...whenDev(() => ({ publicPath: '/', clean: true })),
        ...whenProd(() => ({ publicPath: 'auto', clean: true })),
      };

      return webpackConfig;
    },
  },
};
