{"name": "nv2-core-header-ui", "version": "0.1.0", "private": true, "dependencies": {"@craco/craco": "^6.4.5", "@mui/material": "^5.10.1", "@nv2/nv2-pkg-js-shared-components": "^2.42.5", "@nv2/nv2-pkg-js-theme": "^2.11.3", "@reduxjs/toolkit": "^2.6.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "ajv": "^8.17.1", "axios": "^1.8.4", "core-js": "^2.6.12", "craco-sass-resources-loader": "^1.1.0", "postcss": "^8.5.3", "postcss-loader": "^7.3.4", "prop-types": "^15.8.1", "react": "^18.2.0", "react-cookie": "^4.1.1", "react-dom": "^18.2.0", "react-redux": "^9.2.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "redux-logger": "^3.0.6", "sass-loader": "^16.0.5"}, "scripts": {"start": "GENERATE_SOURCEMAP=false PORT=3001 craco start", "start:on-windows": "set GENERATE_SOURCEMAP=false && set PORT=3001 && craco start", "build": "craco build", "lint": "eslint --ext .js --ext .jsx src", "stylelint": "stylelint \"**/*.scss\"", "test": "jest --maxWorkers=10% --config ./jest.config.js --collectCoverage", "test:coverage": "CI=true npm test -- --env=jsdom --coverage", "eject": "react-scripts eject", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.19.0", "@babel/plugin-proposal-export-default-from": "^7.18.10", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@testing-library/user-event": "^14.4.3", "axios-mock-adapter": "^1.21.1", "eslint": "^9.1.7", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.5", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.1.2", "jest-css-modules-transform": "^4.4.2", "jest-environment-jsdom": "^29.0.3", "redux-mock-store": "^1.5.4", "stylelint": "^14.13.0", "stylelint-config-standard-scss": "^5.0.0", "stylelint-scss": "^4.3.0"}}