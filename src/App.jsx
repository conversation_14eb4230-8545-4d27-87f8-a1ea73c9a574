import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { Route, Routes, useNavigate } from 'react-router-dom';
import { CookiesProvider, useCookies } from 'react-cookie';
import { ThemeProvider } from '@mui/material/styles';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

import { HTTPService, CookiesService } from 'core/services';
import { coreAxios } from 'core/services/HTTPService';
import getAppVariablesAction from 'core/state/AppVariables/actions';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import getUserCredentialAction from 'features/GetUserCredential/actions';
import getShortApplicationsAction from 'features/GetShortApplications/actions';
import getApplicationsManagementAction from 'features/GetApplicationsManagement/actions';
import { logoutUserAccount } from 'api.service';
import getAccessAction from 'features/GetAccess/actions';

import MainHeader from 'features/MainHeader';

import './App.scss';

const App = ({
  breadcrumbsConfig,
  toggleSidebar,
  isSidebarCollapsed,
  collapseSidebar,
}) => {
  const navigate = useNavigate();
  const [cookies] = useCookies();
  const dispatch = useDispatch();
  const appVariables = useSelector((state) => state.appVariables.appVariables);
  const userDetails = useSelector((state) => state?.userCredential?.userCredential)
  const appVariablesIsLoading = useSelector((state) => state.appVariables.isLoading);
  const isDevelopmentMode = process.env.NODE_ENV === 'development';
  const themeName = appVariables?.themeName;
  const currentTheme = themeConfig[themeName];

  const getProdLogOutUrl = (keycloakLogOutUrl) => {
    const keycloakLogoutUrlFull = `${keycloakLogOutUrl}?redirect_uri=${window.location.origin}`;

    return `/oauth2/sign_out?rd=${encodeURIComponent(keycloakLogoutUrlFull)}`;
  };

  const logOut = async (url) => {
    window.location.replace(url);
  };

  const getLogoutUrl = (url) => (isDevelopmentMode
    ? process.env.REACT_APP_LOGOUT_URL
    : getProdLogOutUrl(url));

  const redirectToLogin = () => {
    const currentPathname = window.location.href;
    const entryPointUrl = `${process.env.REACT_APP_LOGIN_URL}?entryPath=${currentPathname}`;

    navigate(entryPointUrl);
  };

  const authenticateForDevelopmentMode = () => {
    const cookieDoesntHaveAccessToken = !CookiesService.getAccessToken(cookies).value;

    if (cookieDoesntHaveAccessToken) {
      redirectToLogin();
    }

    HTTPService.setAccessToken(coreAxios, cookies);
  };

  const setupInitialData = async () => {
    const data = await dispatch(getAppVariablesAction());
    const logoutUrl = getLogoutUrl(data?.themeName === 'bt' ? data?.coreUiUrl : data.keycloakLogoutUrl);

    if (isDevelopmentMode) {
      authenticateForDevelopmentMode();
    }

    HTTPService.setDefaultGlobalConfig(coreAxios, data?.coreApiUrl);

    dispatch(getUserCredentialAction());
    dispatch(getShortApplicationsAction());
    dispatch(getAccessAction());
    dispatch(getApplicationsManagementAction());

    HTTPService.setCorsError(coreAxios, logOut, logoutUrl);
  };

  const getCurrentThemeColors = (color) => getBrandColors(color, themeName);

  useEffect(() => {
    setupInitialData();
  }, []);

  const logoutAccount = async () => {
    try {
      await logoutUserAccount(userDetails?.id)
      window.location.replace(appVariables.coreUiUrl)
    } catch (er) {
      console.error('er: ', er);
    }
  }

  return appVariables && !appVariablesIsLoading
    ? (
      <ThemeProvider
        theme={theme(currentTheme)}
      >
        <CookiesProvider>
          <Routes>
            <Route
              path="/*"
              element={(
                <MainHeader
                  logOut={() => themeName === 'bt' ? logoutAccount() : logOut(getLogoutUrl(appVariables.keycloakLogoutUrl))}
                  hubspotMeetingUrl={appVariables.hubspotMeetingUrl}
                  themeName={themeName}
                  getCurrentThemeColors={getCurrentThemeColors}
                  breadcrumbsConfig={breadcrumbsConfig}
                  coreUrl={appVariables.coreUiUrl}
                  toggleSidebar={toggleSidebar}
                  isSidebarCollapsed={isSidebarCollapsed}
                  collapseSidebar={collapseSidebar}
                />
              )}
            />
          </Routes>
        </CookiesProvider>
      </ThemeProvider>
    ) : '';
};

App.propTypes = {
  breadcrumbsConfig: PropTypes.instanceOf(Array),
  toggleSidebar: PropTypes.func,
  isSidebarCollapsed: PropTypes.bool,
  collapseSidebar: PropTypes.func,
};

App.defaultProps = {
  breadcrumbsConfig: [],
  toggleSidebar: () => { },
  isSidebarCollapsed: true,
  collapseSidebar: () => { },
};

export default App;
