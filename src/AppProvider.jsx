import React from 'react';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';

import store from 'core/store';
import PropTypes from 'prop-types';
import App from './App';

const AppProvider = ({
  breadcrumbsConfig = [],
  toggleSidebar = () => {},
  isSidebarCollapsed = true,
  collapseSidebar = () => {},
}) => (
  <Provider store={store}>
    <BrowserRouter>
      <App
        breadcrumbsConfig={breadcrumbsConfig}
        toggleSidebar={toggleSidebar}
        isSidebarCollapsed={isSidebarCollapsed}
        collapseSidebar={collapseSidebar}
      />
    </BrowserRouter>
  </Provider>
);

AppProvider.propTypes = {
  breadcrumbsConfig: PropTypes.instanceOf(Array),
  toggleSidebar: PropTypes.func,
  isSidebarCollapsed: PropTypes.bool,
  collapseSidebar: PropTypes.func,

};

// AppProvider.defaultProps = {
//   breadcrumbsConfig: [],
//   toggleSidebar: () => {},
//   isSidebarCollapsed: true,
//   collapseSidebar: () => {},
// };

export default AppProvider;
