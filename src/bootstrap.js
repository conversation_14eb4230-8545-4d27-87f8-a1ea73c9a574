import React, { StrictMode } from 'react';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { createRoot } from 'react-dom/client';

import AppProvider from './AppProvider';

const rootElement = document.querySelector('#nv2-core-header-ui');
const root = createRoot(rootElement);

root.render(
  <StrictMode>
    <BrowserRouter>
      <AppProvider />
    </BrowserRouter>
  </StrictMode>,
);
