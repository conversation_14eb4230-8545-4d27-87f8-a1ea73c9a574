import CookiesService from 'core/services/CookiesService';

const unAuthorizedCodeStatus = 401;

export const setRequestHeadersInterceptor = (prevConfig, cookies) => {
  const config = prevConfig;
  if (!config.headers.Authorization) {
    const accessTokenValue = CookiesService.getAccessToken(cookies).value;

    config.headers.Authorization = `Bearer ${accessTokenValue}`;
    config.headers['Cache-control'] = 'no-cache';
  }

  return config;
};

export const setRequestErrorInterceptor = (error, redirectToLogout, logoutUrl) => {
  const statusCode = error.response?.status;

  if (statusCode === unAuthorizedCodeStatus) {
    redirectToLogout(logoutUrl);

    return;
  }

  return Promise.reject(error);
};
