import { combineReducers } from '@reduxjs/toolkit';
import toastrReducer from '@nv2/nv2-pkg-js-shared-components/lib/Toastr/reducer';

import appVariablesReducer from 'core/state/AppVariables/reducer';
import userCredentialReducer from 'features/GetUserCredential/reducer';
import shortApplicationsReducer from 'features/GetShortApplications/reducer';
import applicationManagementReducer from 'features/GetApplicationsManagement/reducer';
import getAccessReducer from 'features/GetAccess/reducer';

const rootReducer = combineReducers({
  ...toastrReducer,
  appVariables: appVariablesReducer,
  userCredential: userCredentialReducer,
  shortApplications: shortApplicationsReducer,
  applicationsManagement: applicationManagementReducer,
  access: getAccessReducer,
});

export default rootReducer;
