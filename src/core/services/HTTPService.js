import axios from 'axios';

import {
  setRequestHeadersInterceptor,
  setRequestErrorInterceptor,
} from 'core/interceptors';

const coreAxios = axios.create();

class HTTPService {
  static setDefaultGlobalConfig(axiosInstance, apiUrl) {
    axiosInstance.defaults.baseURL = apiUrl;
    axiosInstance.defaults.withCredentials = true;
  }

  static setAccessToken(axiosInstance, cookies) {
    axiosInstance.interceptors.request
      .use((prevConfig) => setRequestHeadersInterceptor(prevConfig, cookies));
  }

  static setCorsError(axiosInstance, redirectToLogout, logoutUrl) {
    axiosInstance.interceptors.response.use((res) => res, (error) => {
      setRequestErrorInterceptor(error, redirectToLogout, logoutUrl);
    });
  }

  static getController() {
    return new AbortController();
  }

  static cancelRequest(controller) {
    controller.abort();
  }
}

export {
  HTTPService,
  coreAxios,
};
