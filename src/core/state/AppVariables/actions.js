import { ConfigSettingsService } from 'core/services';
import {
  getAppVariablesFailure,
  getAppVariablesRequest,
  getAppVariablesSuccess,
} from './actionsCreators';

export const getAppVariablesAction = () => async (dispatch) => {
  try {
    dispatch(getAppVariablesRequest());

    const { data } = await ConfigSettingsService.getAppVariables();

    dispatch(getAppVariablesSuccess(data));

    return data;
  } catch (error) {
    dispatch(getAppVariablesFailure(error));
    throw error;
  }
};

export default getAppVariablesAction;
