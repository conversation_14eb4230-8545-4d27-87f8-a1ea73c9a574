import { configureStore } from '@reduxjs/toolkit';
import rootReducer from 'core/rootReducer';
import logger from 'redux-logger';

const isDevelopment = process.env.NODE_ENV === 'development';
const middlewares = [];

if (isDevelopment) {
  middlewares.push(logger);
}

const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) => getDefaultMiddleware(
    {
      immutableCheck: false,
      serializableCheck: false,
    },
  ).concat(middlewares),
});

export default store;
