import {
  getAccessRequest,
  getAccessSuccess,
  getAccessFailure,
} from './actionsCreators';

import getAccess from './api.service';

const getAccessAction = () => async (dispatch) => {
  try {
    dispatch(getAccessRequest());

    const { data } = await getAccess();

    dispatch(getAccessSuccess(data));

    return data;
  } catch (error) {
    dispatch(getAccessFailure(error));

    return error;
  }
};

export default getAccessAction;
