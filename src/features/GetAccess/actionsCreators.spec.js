import {
  GET_ACCESS_REQUEST,
  GET_ACCESS_SUCCESS,
  GET_ACCESS_FAILURE,
} from './actionTypes';

import {
  getAccessRequest,
  getAccessSuccess,
  getAccessFailure,
} from './actionsCreators';

describe('getAccess: actionCreators', () => {
  test('should create action to getAccessRequest', () => {
    const expectedAction = {
      type: GET_ACCESS_REQUEST,
    };

    expect(getAccessRequest()).toEqual(expectedAction);
  });

  test('should create action to getAccessSuccess', () => {
    const data = '';
    const expectedAction = {
      type: GET_ACCESS_SUCCESS,
      data,
    };

    expect(getAccessSuccess(data)).toEqual(expectedAction);
  });

  test('should create action to getAccessFailure', () => {
    const error = 'test error';
    const expectedAction = {
      type: GET_ACCESS_FAILURE,
      error,
    };

    expect(getAccessFailure(error)).toEqual(expectedAction);
  });
});
