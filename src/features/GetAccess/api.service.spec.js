import { coreAxios } from 'core/services/HTTPService';
import getAccess from './api.service';

jest.mock('core/services/HTTPService');

describe('getAccess:', () => {
  test('should get successfully data from an API', async () => {
    const data = '';

    coreAxios.get.mockImplementationOnce(() => Promise.resolve(data));

    await expect(getAccess()).resolves.toEqual(data);
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    coreAxios.get.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(getAccess()).rejects.toThrow(errorMessage);
  });
});
