import {
  GET_ACCESS_REQUEST,
  GET_ACCESS_SUCCESS,
  GET_ACCESS_FAILURE,
} from './actionTypes';

const getAccessReducer = (state = { access: {} }, {
  type, data, error,
}) => {
  switch (type) {
    case GET_ACCESS_REQUEST:
      return {
        ...state,
      };
    case GET_ACCESS_SUCCESS:
      return {
        ...state,
        access: data?.result,
      };
    case GET_ACCESS_FAILURE:
      return {
        ...state,
        error,
      };
    default:
      return state;
  }
};

export default getAccessReducer;
