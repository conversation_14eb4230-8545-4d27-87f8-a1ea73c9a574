import {
  getApplicationsManagementRequest,
  getApplicationsManagementSuccess,
  getApplicationsManagementFailure,
} from './actionsCreators';

import getApplicationsManagement from './api.service';

const getApplicationsManagementAction = () => async (dispatch) => {
  try {
    dispatch(getApplicationsManagementRequest());

    const { data } = await getApplicationsManagement();

    dispatch(getApplicationsManagementSuccess(data));
  } catch (error) {
    dispatch(getApplicationsManagementFailure(error));
    throw error;
  }
};

export default getApplicationsManagementAction;
