import {
  GET_APPLICATIONS_MANAGEMENT_REQUEST,
  GET_APPLICATIONS_MANAGEMENT_SUCCESS,
  GET_APPLICATIONS_MANAGEMENT_FAILURE,
} from './actionTypes';

export const getApplicationsManagementRequest = () => (
  { type: GET_APPLICATIONS_MANAGEMENT_REQUEST });
export const getApplicationsManagementSuccess = (data) => (
  { type: GET_APPLICATIONS_MANAGEMENT_SUCCESS, data });
export const getApplicationsManagementFailure = (error) => (
  { type: GET_APPLICATIONS_MANAGEMENT_FAILURE, error });
