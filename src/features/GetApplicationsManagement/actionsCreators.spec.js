import {
  GET_APPLICATIONS_MANAGEMENT_REQUEST,
  GET_APPLICATIONS_MANAGEMENT_SUCCESS,
  GET_APPLICATIONS_MANAGEMENT_FAILURE,
} from './actionTypes';
import {
  getApplicationsManagementRequest,
  getApplicationsManagementSuccess,
  getApplicationsManagementFailure,
} from './actionsCreators';

describe('GetApplicationsManagement: actionCreators', () => {
  test('should create action to getApplicationsManagementRequest', () => {
    const expectedAction = {
      type: GET_APPLICATIONS_MANAGEMENT_REQUEST,
    };

    expect(getApplicationsManagementRequest()).toEqual(expectedAction);
  });

  test('should create action to getApplicationsManagementSuccess', () => {
    const data = {
      title: 'test title',
      description: 'test description',
      ur: 'test url',
    };
    const expectedAction = {
      type: GET_APPLICATIONS_MANAGEMENT_SUCCESS,
      data,
    };

    expect(getApplicationsManagementSuccess(data)).toEqual(expectedAction);
  });

  test('should create action to getApplicationsManagementFailure', () => {
    const error = 'test error';
    const expectedAction = {
      type: GET_APPLICATIONS_MANAGEMENT_FAILURE,
      error,
    };

    expect(getApplicationsManagementFailure(error)).toEqual(expectedAction);
  });
});
