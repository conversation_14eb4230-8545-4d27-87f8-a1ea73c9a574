import { coreAxios } from 'core/services/HTTPService';
import getApplicationsManagement from './api.service';

jest.mock('core/services/HTTPService');

describe('GetApplicationsManagement', () => {
  test('should get successfully data from an API', async () => {
    const data = {
      title: 'test title',
      description: 'test description',
      ur: 'test url',
    };

    coreAxios.get.mockImplementationOnce(() => Promise.resolve(data));

    await expect(getApplicationsManagement()).resolves.toEqual(data);
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    coreAxios.get.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(getApplicationsManagement()).rejects.toThrow(errorMessage);
  });

  test('should called get request with applications management url', async () => {
    expect(coreAxios.get).toHaveBeenCalledWith('/applications/built-in');
  });
});
