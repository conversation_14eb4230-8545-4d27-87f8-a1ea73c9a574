import {
  GET_APPLICATIONS_MANAGEMENT_REQUEST,
  GET_APPLICATIONS_MANAGEMENT_SUCCESS,
  GET_APPLICATIONS_MANAGEMENT_FAILURE,
} from './actionTypes';

const applicationManagementReducer = (state = {
  applications: [],
  isLoading: false,
  isClient: false,
  success: false,
}, {
  type, data, error,
}) => {
  switch (type) {
    case GET_APPLICATIONS_MANAGEMENT_REQUEST:
      return {
        ...state,
        isLoading: true,
        applications: [],
        success: false,
      };
    case GET_APPLICATIONS_MANAGEMENT_SUCCESS:
      return {
        ...state,
        applications: data,
        isLoading: false,
        success: true,
      };
    case GET_APPLICATIONS_MANAGEMENT_FAILURE:
      return {
        ...state,
        isLoading: false,
        isClient: true,
        error,
      };
    default:
      return state;
  }
};

export default applicationManagementReducer;
