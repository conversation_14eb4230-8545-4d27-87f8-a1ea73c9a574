import {
  getShortApplicationsFailure,
  getShortApplicationsRequest,
  getShortApplicationsSuccess,
} from './actionsCreators';

import getShortApplications from './api.service';

const getShortApplicationsAction = () => async (dispatch) => {
  try {
    dispatch(getShortApplicationsRequest());

    const { data } = await getShortApplications();

    dispatch(getShortApplicationsSuccess(data));
  } catch (error) {
    dispatch(getShortApplicationsFailure(error));
    throw error;
  }
};

export default getShortApplicationsAction;
