import {
  GET_SHORT_APPLICATIONS_FAILURE,
  GET_SHORT_APPLICATIONS_REQUEST,
  GET_SHORT_APPLICATIONS_SUCCESS,
} from './actionTypes';
import {
  getShortApplicationsFailure,
  getShortApplicationsRequest,
  getShortApplicationsSuccess,
} from './actionsCreators';

describe('GetShortApplications: actionCreators', () => {
  test('should create action to getShortApplicationsRequest', () => {
    const expectedAction = {
      type: GET_SHORT_APPLICATIONS_REQUEST,
    };

    expect(getShortApplicationsRequest()).toEqual(expectedAction);
  });

  test('should create action to getShortApplicationsSuccess', () => {
    const data = {
      title: 'test title',
      description: 'test description',
      ur: 'test url',
    };
    const expectedAction = {
      type: GET_SHORT_APPLICATIONS_SUCCESS,
      data,
    };

    expect(getShortApplicationsSuccess(data)).toEqual(expectedAction);
  });

  test('should create action to getShortApplicationsFailure', () => {
    const error = 'test error';
    const expectedAction = {
      type: GET_SHORT_APPLICATIONS_FAILURE,
      error,
    };

    expect(getShortApplicationsFailure(error)).toEqual(expectedAction);
  });
});
