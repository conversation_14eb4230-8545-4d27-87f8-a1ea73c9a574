import {
  GET_SHORT_APPLICATIONS_REQUEST,
  GET_SHORT_APPLICATIONS_SUCCESS,
  GET_SHORT_APPLICATIONS_FAILURE,
} from './actionTypes';

const shortApplicationsReducer = (state = {
  applications: [],
  applicationsManagement: [],
  isLoading: false,
  isClient: false,
}, {
  type, data, error,
}) => {
  switch (type) {
    case GET_SHORT_APPLICATIONS_REQUEST:
      return {
        ...state,
        applications: [],
        isLoading: true,
      };
    case GET_SHORT_APPLICATIONS_SUCCESS:
      return {
        ...state,
        applications: data,
        isLoading: false,
      };
    case GET_SHORT_APPLICATIONS_FAILURE:
      return {
        ...state,
        isLoading: false,
        error,
      };
    default:
      return state;
  }
};

export default shortApplicationsReducer;
