import {
  getUserCredentialRequest,
  getUserCredentialSuc<PERSON>,
  getUserCredentialFailure,
} from './actionsCreators';

import getUserCredential from './api.service';

const getUserCredentialAction = () => async (dispatch) => {
  try {
    dispatch(getUserCredentialRequest());

    const { data } = await getUserCredential();

    dispatch(getUserCredentialSuccess(data));
  } catch (error) {
    dispatch(getUserCredentialFailure(error));
  }
};

export default getUserCredentialAction;
