import {
  GET_USER_CREDENTIAL_REQUEST,
  GET_USER_CREDENTIAL_SUCCESS,
  GET_USER_CREDENTIAL_FAILURE,
} from './actionTypes';

export const getUserCredentialRequest = () => ({ type: GET_USER_CREDENTIAL_REQUEST });
export const getUserCredentialSuccess = (data) => (
  { type: GET_USER_CREDENTIAL_SUCCESS, data });
export const getUserCredentialFailure = (error) => (
  { type: GET_USER_CREDENTIAL_FAILURE, error });
