import {
  GET_USER_CREDENTIAL_REQUEST,
  GET_USER_CREDENTIAL_SUCCESS,
  GET_USER_CREDENTIAL_FAILURE,
} from './actionTypes';

import {
  getUserCredentialRequest,
  getUserCredentialSuccess,
  getUserCredentialFailure,
} from './actionsCreators';

describe('GetUserCredential: actionCreators', () => {
  test('should create action to getUserCredentialRequest', () => {
    const expectedAction = {
      type: GET_USER_CREDENTIAL_REQUEST,
    };

    expect(getUserCredentialRequest()).toEqual(expectedAction);
  });

  test('should create action to getUserCredentialSuccess', () => {
    const data = '';
    const expectedAction = {
      type: GET_USER_CREDENTIAL_SUCCESS,
      data,
    };

    expect(getUserCredentialSuccess(data)).toEqual(expectedAction);
  });

  test('should create action to getUserCredentialFailure', () => {
    const error = 'test error';
    const expectedAction = {
      type: GET_USER_CREDENTIAL_FAILURE,
      error,
    };

    expect(getUserCredentialFailure(error)).toEqual(expectedAction);
  });
});
