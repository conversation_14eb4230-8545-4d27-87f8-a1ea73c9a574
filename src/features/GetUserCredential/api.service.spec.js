import { coreAxios } from 'core/services/HTTPService';
import getUserCredential from './api.service';

jest.mock('core/services/HTTPService');

describe('GetUserCredential:', () => {
  test('should get successfully data from an API', async () => {
    const data = '';

    coreAxios.get.mockImplementationOnce(() => Promise.resolve(data));

    await expect(getUserCredential()).resolves.toEqual(data);
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    coreAxios.get.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(getUserCredential()).rejects.toThrow(errorMessage);
  });
});
