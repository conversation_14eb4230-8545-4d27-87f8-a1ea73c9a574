import {
  GET_USER_CREDENTIAL_REQUEST,
  GET_USER_CREDENTIAL_SUCCESS,
  GET_USER_CREDENTIAL_FAILURE,
} from './actionTypes';

const userCredentialReducer = (state = { userCredential: { organization: {} } }, {
  type, data, error,
}) => {
  switch (type) {
    case GET_USER_CREDENTIAL_REQUEST:
      return {
        ...state,
        isLoading: true,
      };
    case GET_USER_CREDENTIAL_SUCCESS:
      return {
        ...state,
        userCredential: data,
        isLoading: false,
      };
    case GET_USER_CREDENTIAL_FAILURE:
      return {
        ...state,
        error,
        isLoading: false,
      };
    default:
      return state;
  }
};

export default userCredentialReducer;
