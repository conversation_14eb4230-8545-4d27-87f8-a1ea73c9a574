import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';

import Header from '@nv2/nv2-pkg-js-shared-components/lib/Header';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';

import hostNameRegExp from 'core/configs/hostNameRegExp';
import organizationTypes from 'core/configs/organizationTypes';
import { REPOSITORY, ROUTE_PERMISSION } from 'constant';

const administrationTitle = 'Administration';

const MainHeader = ({
  logOut,
  hubspotMeetingUrl,
  themeName,
  getCurrentThemeColors,
  breadcrumbsConfig,
  collapseSidebar,
  toggleSidebar,
  isSidebarCollapsed,
  coreUrl,
}) => {
  const userInfo = useSelector((state) => state.userCredential.userCredential);
  const access = useSelector((state) => state?.access?.access);
  const hostName = window.location.origin;
  const userName = `${userInfo?.firstName || ''} ${userInfo?.lastName || ''}`.trim();
  const userEmail = userInfo?.email || '';
  const applicationsData = useSelector((state) => state.shortApplications.applications);
  const isDistributor = userInfo?.organization?.type === organizationTypes.distributor;
   
  const hubspotMeetingUrlGivenTheRole = isDistributor ? '' : hubspotMeetingUrl;
  const applicationsManagementData = useSelector((state) => (
    state.applicationsManagement.applications
  )) || [];
  const isLoadingApplicationsManagementData = useSelector((state) => (
    state.applicationsManagement.isLoading
  ));
  const isLoadingUserInfo = useSelector((state) => state.userCredential.isLoading);
  const isLoadingShortApplications = useSelector((state) => (
    state.shortApplications.isLoading
  ));

  const hasViewInvoicesPermission = access?.length ? access?.some(
    (item) => item.name === REPOSITORY?.SIM_AUTOMATION
    && item.permission.some((perm) => perm?.name === ROUTE_PERMISSION?.VIEW_RULE_DETAIL),
  ) : true;

  const applicationsManagementDataBaseRoutes = [];

  // ************** previous code of navbar kept for reference ************

  // applicationsManagementData?.forEach((item) => {
  //   const isInternalLink = item.url.match(hostNameRegExp)[0] === `${hostName}/`;
  //   const url = isInternalLink ? item.url.replace(hostNameRegExp, '/') : item.url;

  //   if (themeName === 'bt' && item?.title === REPOSITORY?.AUTOMATION_RULES) {
  //     if (hasViewInvoicesPermission) {
  //       applicationsManagementDataBaseRoutes.push({ ...item, url });
  //     }
  //   } else {
  //     applicationsManagementDataBaseRoutes.push({ ...item, url });
  //   }
  // });

  // ************** till here  ************

  applicationsManagementData?.forEach((item) => {
    const isInternalLink = item.url.match(hostNameRegExp)[0] === `${hostName}/`;

    let url;
    if (themeName === 'bt') {
      url = item.url;
    } else {
      url = isInternalLink ? item.url.replace(hostNameRegExp, '/') : item.url;
    }

    if (themeName === 'bt' && item?.title === REPOSITORY?.AUTOMATION_RULES) {
      if (hasViewInvoicesPermission) {
        applicationsManagementDataBaseRoutes.push({ ...item, url });
      }
    } else {
      applicationsManagementDataBaseRoutes.push({ ...item, url });
    }
  });

  const getAdministrationData = () => {
    const administrationData = {};

     
    for (const app of applicationsManagementDataBaseRoutes) {
      if (app.title === administrationTitle) {
         
        for (const key in app) {
          administrationData[key] = app[key];
        }
      }
    }

    return administrationData;
  };


  return (
    <Header
      signOut={logOut}
      userName={userName}
      userEmail={userEmail}
      primaryColor={themeConfig[themeName].primaryColor}
      logo={themeConfig[themeName]?.logo}
      logoMob={themeConfig[themeName]?.logoMob}
      getCurrentThemeColors={getCurrentThemeColors}
      darkColor500={themeConfig[themeName].darkColor}
      collapseSidebar={collapseSidebar}
      toggleSidebar={toggleSidebar}
      collapsed={isSidebarCollapsed}
      headerMenuData={applicationsManagementDataBaseRoutes}
      breadcrumbs={breadcrumbsConfig}
      applicationsData={applicationsData}
      administrationData={getAdministrationData}
      hubspotMeetingUrl={hubspotMeetingUrlGivenTheRole}
      isLoadingUserInfo={isLoadingUserInfo}
      isLoadingApplicationsManagementData={isLoadingApplicationsManagementData}
      isLoadingShortApplications={isLoadingShortApplications}
      coreUrl={coreUrl}
    />
  );
};

MainHeader.propTypes = {
  logOut: PropTypes.func.isRequired,
  themeName: PropTypes.string.isRequired,
  getCurrentThemeColors: PropTypes.func.isRequired,
  hubspotMeetingUrl: PropTypes.string,
  breadcrumbsConfig: PropTypes.instanceOf(Array),
  collapseSidebar: PropTypes.func,
  toggleSidebar: PropTypes.func,
  isSidebarCollapsed: PropTypes.bool,
  coreUrl: PropTypes.string,
};

MainHeader.defaultProps = {
  hubspotMeetingUrl: '',
  breadcrumbsConfig: [],
  collapseSidebar: () => { },
  toggleSidebar: () => { },
  isSidebarCollapsed: true,
  coreUrl: '',
};

export default MainHeader;
